<template>
  <div class="frontend-page">
    <!-- 页面头部 -->
    <header class="frontend-header">
      <div class="header-container">
        <div class="logo-section">
          <Icon icon="ep:data-analysis" class="logo-icon" />
          <h1 class="site-title">数据查询平台</h1>
        </div>
        <div class="header-actions">
          <el-button 
            v-if="!isLoggedIn" 
            type="primary" 
            @click="showLoginDialog = true"
            class="login-btn"
          >
            <Icon icon="ep:user" class="btn-icon" />
            登录
          </el-button>
          <div v-else class="user-info">
            <el-dropdown @command="handleUserCommand">
              <span class="user-dropdown">
                <el-avatar :size="32" :src="userInfo.avatar" class="user-avatar">
                  <Icon icon="ep:user" />
                </el-avatar>
                <span class="username">{{ userInfo.nickname || userInfo.username }}</span>
                <Icon icon="ep:arrow-down" class="dropdown-icon" />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="logout" divided>
                    <Icon icon="ep:switch-button" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="frontend-main">
      <div class="main-container">
        <!-- 欢迎区域 -->
        <section class="welcome-section">
          <div class="welcome-content">
            <h2 class="welcome-title">欢迎使用数据查询平台</h2>
            <p class="welcome-description">
              提供高效、准确的数据查询服务，支持多种查询方式和数据格式
            </p>
          </div>
        </section>

        <!-- 查询区域 - 复用现有的查询功能 -->
        <section class="query-section">
          <div class="query-container">
            <!-- 搜索工作栏 -->
            <div class="search-container">
              <div class="search-header">
                <h3 class="search-title">
                  <Icon icon="ep:search" class="title-icon" />
                  数据查询
                </h3>
                <p class="search-subtitle">请输入查询条件进行精确搜索</p>
              </div>

              <el-form
                class="search-form"
                :model="queryParams"
                ref="queryFormRef"
                :inline="true"
                label-width="80px"
              >
                <div class="form-row">
                  <el-form-item label="查询结果" prop="value" class="form-item-custom">
                    <el-input
                      v-model="queryParams.value"
                      placeholder="请输入精确结果进行查询"
                      clearable
                      @keyup.enter="handleQuery"
                      class="search-input"
                      prefix-icon="Search"
                    />
                  </el-form-item>
                </div>

                <div class="form-actions">
                  <el-button type="primary" @click="handleQuery" class="search-btn">
                    <Icon icon="ep:search" class="btn-icon" />
                    搜索查询
                  </el-button>
                  <el-button @click="resetQuery" class="reset-btn">
                    <Icon icon="ep:refresh" class="btn-icon" />
                    重置查询
                  </el-button>
                </div>
              </el-form>
            </div>

            <!-- 结果展示区域 -->
            <div class="result-container">
              <!-- 结果统计 -->
              <div v-if="hasSearched" class="result-stats">
                <div class="stats-item">
                  <Icon icon="ep:document" class="stats-icon" />
                  <span class="stats-text">共找到 <strong>{{ total }}</strong> 条结果</span>
                </div>
              </div>

              <!-- 数据表格 -->
              <el-table
                row-key="id"
                v-loading="loading"
                :data="list"
                :stripe="true"
                :show-overflow-tooltip="true"
                class="data-table"
                element-loading-text="正在查询数据..."
                element-loading-background="rgba(255, 255, 255, 0.8)"
              >
                <el-table-column label="编号" align="center" prop="id" width="180">
                  <template #default="scope">
                    <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="数据类型" align="center" prop="type" min-width="150">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_DATA_TYPE" :value="scope.row.type" />
                  </template>
                </el-table-column>

                <el-table-column label="查询结果" align="left" prop="value" min-width="180">
                  <template #default="scope">
                    <div class="result-cell">
                      <Icon icon="ep:document-copy" class="result-icon" />
                      <span class="result-text">{{ scope.row.value }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="创建时间"
                  align="center"
                  prop="createTime"
                  :formatter="dateFormatter"
                  min-width="180"
                >
                  <template #default="scope">
                    <div class="time-cell">
                      <Icon icon="ep:clock" class="time-icon" />
                      <span>{{ dateFormatter(scope.row, null, scope.row.createTime) }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="批次" align="center" prop="batchId" min-width="150">
                  <template #default="scope">
                    <el-tag type="warning" size="small">{{ scope.row.batchId }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 空状态展示 -->
              <div v-if="!loading && list.length === 0 && hasSearched" class="empty-state">
                <el-empty
                  description="未找到匹配的查询结果"
                  :image-size="120"
                >
                  <template #description>
                    <p class="empty-text">未找到匹配的查询结果</p>
                  </template>
                  <el-button type="primary" @click="resetQuery">
                    重新查询
                  </el-button>
                </el-empty>
              </div>

              <!-- 初始状态 -->
              <div v-if="!loading && !hasSearched" class="welcome-state">
                <el-empty
                  description="开始您的数据查询之旅"
                  :image-size="130"
                >
                  <template #description>
                    <div class="welcome-content">
                      <h4 class="welcome-title">开始您的数据查询</h4>
                      <p class="welcome-text">在上方输入框中输入要查询的内容，点击搜索按钮开始查询</p>
                    </div>
                  </template>
                </el-empty>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 页面底部 -->
    <footer class="frontend-footer">
      <div class="footer-container">
        <p class="copyright">© 2024 数据查询平台. All rights reserved.</p>
      </div>
    </footer>

    <!-- 登录弹窗 -->
    <LoginDialog 
      v-model="showLoginDialog" 
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { DataInfoApi, DataInfo } from '@/api/system/datainfo'
import { useUserStore } from '@/store/modules/user'
import { DictTag } from '@/components/DictTag'
import LoginDialog from './components/LoginDialog.vue'

/** 前台数据查询页面 */
defineOptions({ name: 'FrontendIndex' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const userStore = useUserStore()

// 用户登录状态
const isLoggedIn = computed(() => userStore.getIsSetUser)
const userInfo = computed(() => userStore.getUser)

// 登录弹窗显示状态
const showLoginDialog = ref(false)

// 查询相关状态
const loading = ref(false) // 列表的加载中
const list = ref<DataInfo[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const hasSearched = ref(false) // 是否已经搜索过
const queryParams = reactive({
  value: undefined, // 只保留结果查询，支持精确匹配
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 如果没有输入查询条件，不进行查询
    if (!queryParams.value && (!queryParams.createTime || queryParams.createTime.length === 0)) {
      list.value = []
      total.value = 0
      loading.value = false
      return
    }

    // 调用API进行精确查询
    const data = await DataInfoApi.getAnoPage(queryParams)
    list.value = data.list
    total.value = data.total
    hasSearched.value = true
  } catch (error) {
    console.error('查询失败:', error)
    message.error('查询失败，请稍后重试')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  if (!queryParams.value?.trim()) {
    message.warning('请输入要查询的结果')
    return
  }
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  list.value = []
  total.value = 0
  hasSearched.value = false
}

/** 处理用户下拉菜单命令 */
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'logout':
      handleLogout()
      break
  }
}

/** 处理登录成功 */
const handleLoginSuccess = () => {
  showLoginDialog.value = false
  message.success('登录成功')
}

/** 处理退出登录 */
const handleLogout = async () => {
  try {
    await userStore.loginOut()
    message.success('退出登录成功')
    // 清空查询结果
    resetQuery()
  } catch (error) {
    console.error('退出登录失败:', error)
    message.error('退出登录失败')
  }
}

/** 初始化 */
onMounted(() => {
  // 前台页面不自动加载数据，需要用户主动搜索
})
</script>

<style scoped>
/* 前台页面整体样式 */
.frontend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/* 页面头部样式 */
.frontend-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: #1890ff;
}

.site-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.login-btn {
  padding: 8px 20px;
  border-radius: 20px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.btn-icon {
  margin-right: 4px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-dropdown:hover {
  background: rgba(24, 144, 255, 0.1);
}

.user-avatar {
  border: 2px solid #1890ff;
}

.username {
  font-weight: 500;
  color: #262626;
}

.dropdown-icon {
  font-size: 12px;
  color: #8c8c8c;
}

/* 主要内容区域 */
.frontend-main {
  flex: 1;
  padding: 40px 0;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-title {
  font-size: 36px;
  font-weight: 700;
  color: #262626;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-description {
  font-size: 18px;
  color: #595959;
  line-height: 1.6;
  margin: 0;
}

/* 查询区域 */
.query-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.query-container {
  padding: 0;
}

/* 搜索容器样式 */
.search-container {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 32px;
  color: white;
}

.search-header {
  text-align: center;
  margin-bottom: 24px;
}

.search-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.title-icon {
  font-size: 26px;
}

.search-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.search-form {
  background: rgba(255, 255, 255, 0.98);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.form-item-custom {
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
}


.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.search-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 8px;
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.reset-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 8px;
  color: #595959;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 结果容器样式 */
.result-container {
  padding: 24px;
}

.result-stats {
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  color: #1890ff;
  font-size: 16px;
}

.stats-text {
  color: #262626;
  font-size: 14px;
}

/* 表格样式 */
.data-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-table) {
  --el-table-border-color: #e8e8e8;
  --el-table-bg-color: #ffffff;
  --el-table-tr-bg-color: #ffffff;
}

:deep(.el-table th) {
  background: #fafafa;
  color: #262626;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  color: #595959;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafafa;
}

:deep(.el-table tr:hover > td) {
  background-color: #e6f7ff !important;
}

.result-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.result-icon {
  color: #1890ff;
  font-size: 14px;
}

.result-text {
  font-weight: 500;
  color: #262626;
}

.time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 13px;
  color: #8c8c8c;
}

.time-icon {
  font-size: 14px;
}

/* 空状态样式 */
.empty-state, .welcome-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 16px;
  color: #262626;
}

.welcome-content h4 {
  font-size: 18px;
  color: #262626;
  margin: 16px 0 12px;
  font-weight: 600;
}

.welcome-text {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.6;
}

/* 页面底部 */
.frontend-footer {
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.copyright {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  height: 22px;
  line-height: 20px;
  padding: 0 7px;
}

:deep(.el-tag--info) {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
  color: #595959;
}

:deep(.el-tag--warning) {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #d46b08;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }

  .site-title {
    font-size: 20px;
  }

  .main-container {
    padding: 0 16px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-description {
    font-size: 16px;
  }

  .search-container {
    padding: 24px 16px;
  }

  .search-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-btn, .reset-btn {
    width: 100%;
  }

  .result-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .header-container {
    height: 56px;
  }

  .logo-icon {
    font-size: 24px;
  }

  .site-title {
    font-size: 18px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .search-title {
    font-size: 20px;
  }

  .user-dropdown .username {
    display: none;
  }
}

/* 动画效果 */
.query-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  color: #1890ff;
}
</style>
